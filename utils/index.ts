import {ref} from 'vue'
/**
 *  全局 主题数据值
 *  @class light | dark
 */ 
export const GLOBAL_CURRENT_THEME =ref('light');

export const formatThousand = (value: number | string): string => {
  if (value === null || value === undefined || value === '') return '0'

  const num = Number(value)
  if (isNaN(num)) return String(value)

  return num.toLocaleString('en-US')
}

export const convertToLabelValueArray = <T extends Record<string, any>>(
  obj: T,
  keys: Array<keyof T>
): Array<{ label: string; value: T[keyof T] }> => {
  return keys.map((key) => {
    const labelText = String(key)
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim()
    return {
      label: labelText,
      value: obj[key],
    }
  })
}

/**
 * 从GitHub链接或用户名中提取用户名
 * 支持的格式：
 * - username
 * - github.com/username
 * - www.github.com/username
 * - https://github.com/username
 * - https://www.github.com/username
 */
export const extractGitHubUsername = (input: string): string => {
  if (!input || typeof input !== 'string') return ''
  
  const trimmedInput = input.trim()
  
  // 如果输入不包含github.com，直接返回（假设是用户名）
  if (!trimmedInput.toLowerCase().includes('github.com')) {
    return trimmedInput
  }
  
  // 匹配GitHub URL中的用户名
  // 支持: github.com/username, www.github.com/username, https://github.com/username 等
  const githubUrlPattern = /(?:https?:\/\/)?(?:www\.)?github\.com\/([^\/\?\s]+)/i
  const match = trimmedInput.match(githubUrlPattern)
  
  if (match && match[1]) {
    return match[1]
  }
  
  // 如果没有匹配到，返回原始输入
  return trimmedInput
}
