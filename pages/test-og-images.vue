<template>
  <div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-6">OG图片生成测试</h1>
    
    <div class="space-y-6">
      <!-- 测试按钮 -->
      <div class="flex gap-4">
        <button 
          @click="testOgGeneration" 
          :disabled="isGenerating"
          class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {{ isGenerating ? '生成中...' : '测试OG图片生成' }}
        </button>
        <button 
          @click="clearResults" 
          class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
        >
          清除结果
        </button>
      </div>

      <!-- 生成状态 -->
      <div v-if="isGenerating" class="bg-blue-50 p-4 rounded">
        <div class="flex items-center gap-2">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span>正在生成OG图片...</span>
        </div>
        <div class="mt-2 text-sm text-gray-600">
          <p>• 等待DOM渲染完成</p>
          <p>• 等待图片资源加载</p>
          <p>• 处理SVG图标替换</p>
          <p>• 修复背景图片和样式</p>
          <p>• 生成高质量截图</p>
        </div>
      </div>

      <!-- 结果显示 -->
      <div v-if="result" class="space-y-4">
        <h2 class="text-xl font-semibold">生成结果:</h2>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 图片预览 -->
          <div class="border rounded p-4">
            <h3 class="font-medium mb-2">生成的OG图片:</h3>
            <div v-if="result.success" class="space-y-2">
              <img :src="result.imageUrl" alt="Generated OG Image" class="border rounded max-w-full" />
              <div class="text-sm text-gray-600">
                <p><strong>尺寸:</strong> {{ result.dimensions }}</p>
                <p><strong>文件大小:</strong> {{ result.fileSize }}</p>
                <p><strong>生成耗时:</strong> {{ result.duration }}ms</p>
              </div>
            </div>
            <div v-else class="text-red-600">
              <p><strong>生成失败:</strong> {{ result.error }}</p>
            </div>
          </div>

          <!-- 技术细节 -->
          <div class="border rounded p-4">
            <h3 class="font-medium mb-2">技术细节:</h3>
            <div class="text-sm space-y-1">
              <p><strong>隐藏方式:</strong> transform: translateX(-100%)</p>
              <p><strong>SVG处理:</strong> 替换为PNG图片</p>
              <p><strong>背景修复:</strong> 自定义背景图片</p>
              <p><strong>毛玻璃效果:</strong> 替换为实色背景</p>
              <p><strong>版权信息:</strong> 自动添加</p>
            </div>
            
            <div class="mt-4">
              <h4 class="font-medium mb-1">处理的图标:</h4>
              <div class="text-xs text-gray-600 space-y-1">
                <p>• verified → github-verify.png</p>
                <p>• research → overview.png</p>
                <p>• add1 → additions.png</p>
                <p>• trash-bin → deletions.png</p>
                <p>• project → highlight.png</p>
                <p>• stars → stars.png</p>
                <p>• forks → forks.png</p>
                <p>• growth → marketvalue.png</p>
                <p>• growth-investing → yoe.png</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 下载和分享 -->
        <div class="flex gap-4">
          <a 
            v-if="result.success" 
            :href="result.imageUrl" 
            download="og-image-test.png"
            class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            下载图片
          </a>
          <button 
            v-if="result.success"
            @click="copyImageUrl"
            class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
          >
            复制图片URL
          </button>
        </div>
      </div>

      <!-- 说明 -->
      <div class="bg-gray-50 p-4 rounded">
        <h2 class="text-lg font-semibold mb-2">测试说明:</h2>
        <ul class="text-sm space-y-1 list-disc list-inside">
          <li>这个测试会生成一个模拟的GitHub分享卡片OG图片</li>
          <li>包含了所有的图片处理逻辑：SVG替换、背景修复、样式优化</li>
          <li>使用与实际OG图片生成相同的代码逻辑</li>
          <li>可以验证图片是否正确显示所有元素</li>
        </ul>
      </div>
    </div>

    <!-- 隐藏的测试卡片 -->
    <div 
      ref="testCardContainer"
      class="hidden-render-container"
      :style="{
        position: 'fixed',
        top: '0',
        left: '0',
        transform: 'translateX(-100%)',
        zIndex: '-1',
        pointerEvents: 'none'
      }"
    >
      <div
        class="rounded-2xl shadow-xl p-6 w-[1200px] h-[700px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1]"
        data-card-id="share-card-github"
      >
        <!-- 简化的测试内容 -->
        <div class="bg-[url(/image/graphbg.png)] bg-right bg-contain bg-no-repeat">
          <div class="w-[850px]">
            <!-- 顶部用户信息 -->
            <div class="flex items-center justify-start h-[80px]">
              <img src="https://github.com/octocat.png" class="w-20 h-20 rounded-full mr-4" />
              <div class="flex flex-col flex-1 justify-around">
                <div class="flex items-center gap-4">
                  <h2 class="text-xl font-bold">Test User</h2>
                  <div class="flex items-start gap-1 text-sm text-gray-500 flex-1">
                    <svg class="svg-icon w-4 h-4 mt-0.5 flex-shrink-0">
                      <use href="#icon-verified"></use>
                    </svg>
                    <span>Software Engineer</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 测试卡片 -->
            <div class="flex gap-4 mt-6">
              <div class="custom-bg p-4 rounded-lg bg-white/80" style="backdrop-filter: blur(14px);">
                <div class="flex items-center gap-2 mb-2">
                  <svg class="svg-icon w-5 h-5">
                    <use href="#icon-add1"></use>
                  </svg>
                  <span class="text-sm font-medium">Additions</span>
                </div>
                <div class="text-2xl font-bold">1,234</div>
              </div>

              <div class="custom-bg p-4 rounded-lg bg-white/80" style="backdrop-filter: blur(14px);">
                <div class="flex items-center gap-2 mb-2">
                  <svg class="svg-icon w-5 h-5">
                    <use href="#icon-trash-bin"></use>
                  </svg>
                  <span class="text-sm font-medium">Deletions</span>
                </div>
                <div class="text-2xl font-bold">567</div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="absolute top-4 right-4" data-action-buttons>
              <button class="bg-blue-500 text-white px-4 py-2 rounded">Download</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import html2canvas from 'html2canvas-pro'

const testCardContainer = ref<HTMLElement>()
const isGenerating = ref(false)
const result = ref<any>(null)

const testOgGeneration = async () => {
  if (isGenerating.value) return

  isGenerating.value = true
  result.value = null

  try {
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 1000))

    const shareCardElement = document.querySelector('[data-card-id="share-card-github"]')
    if (!shareCardElement) {
      throw new Error('测试卡片元素未找到')
    }

    const startTime = Date.now()

    // 使用与实际OG生成相同的配置
    const canvas = await html2canvas(shareCardElement as HTMLElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 1200,
      height: 630,
      logging: true,
      imageTimeout: 15000,
      foreignObjectRendering: false,
      scrollX: 0,
      scrollY: 0,
      onclone: (clonedDoc) => {
        const clonedElement = clonedDoc.querySelector('[data-card-id="share-card-github"]')
        if (clonedElement) {
          // 替换按钮为版权信息
          const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
          if (buttonContainer) {
            const copyrightDiv = clonedDoc.createElement('div')
            copyrightDiv.style.cssText = 'position: absolute; bottom: 8px; right: 16px; font-size: 12px; color: #666;'
            copyrightDiv.textContent = 'Generated by DINQ'
            buttonContainer.parentNode?.replaceChild(copyrightDiv, buttonContainer)
          }

          // 替换SVG图标
          const svgIconElements = clonedElement.querySelectorAll('svg.svg-icon')
          svgIconElements.forEach((svgEl) => {
            const svgElement = svgEl as SVGElement
            const useElement = svgElement.querySelector('use')
            if (!useElement) return
            
            const iconId = useElement.getAttribute('href')
            if (!iconId) return
            
            const imgElement = clonedDoc.createElement('img')
            
            if (iconId === '#icon-verified') {
              imgElement.src = '/image/sharecard/github-verify.png'
              imgElement.className = 'w-4 h-4 mt-0.5 flex-shrink-0'
            } else if (iconId === '#icon-add1') {
              imgElement.src = '/image/sharecard/additions.png'
              imgElement.className = 'w-5 h-5'
            } else if (iconId === '#icon-trash-bin') {
              imgElement.src = '/image/sharecard/deletions.png'
              imgElement.className = 'w-5 h-5'
            }
            
            if (imgElement.src) {
              svgElement.parentNode?.replaceChild(imgElement, svgElement)
            }
          })
        }
      }
    })

    const imageUrl = canvas.toDataURL('image/png')
    const duration = Date.now() - startTime

    result.value = {
      success: true,
      imageUrl,
      dimensions: `${canvas.width}x${canvas.height}`,
      fileSize: `${Math.round(imageUrl.length * 0.75 / 1024)}KB`,
      duration
    }

  } catch (error: any) {
    result.value = {
      success: false,
      error: error.message
    }
  } finally {
    isGenerating.value = false
  }
}

const clearResults = () => {
  result.value = null
}

const copyImageUrl = async () => {
  if (result.value?.imageUrl) {
    try {
      await navigator.clipboard.writeText(result.value.imageUrl)
      alert('图片URL已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
    }
  }
}

// 设置页面meta
useSeoMeta({
  title: 'OG图片生成测试 - DINQ',
  description: '测试OG图片生成功能，包括SVG替换和样式修复',
})
</script>
