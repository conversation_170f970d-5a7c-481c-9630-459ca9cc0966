<template>
  <!-- 专门用于隐藏渲染的分享卡片组件 -->
  <!-- 使用最佳实践的隐藏方式：确保DOM存在但用户看不到 -->
  <div 
    class="hidden-render-container"
    :style="{
      position: 'fixed',
      left: '-9999px',
      top: '-9999px',
      zIndex: '-1',
      opacity: '0',
      pointerEvents: 'none',
      visibility: 'hidden'
    }"
  >
    <!-- 直接渲染分享卡片内容，不使用ShareCardGithub的弹窗包装 -->
    <div
      class="rounded-2xl shadow-xl p-6 w-[1200px] h-[700px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1] dark:from-[#141415] dark:to-[#141415] border border-transparent dark:border-[#27282D]"
      data-card-id="share-card-github"
    >
      <div class="bg-[url(/image/graphbg.png)] bg-right bg-contain bg-no-repeat">
        <div class="w-[850px]">
          <!-- 顶部用户信息 -->
          <div class="flex items-center justify-start h-[80px]" v-if="user">
            <img :src="user.avatar" class="w-20 h-20 rounded-full mr-4" />
            <div class="flex flex-col flex-1 justify-around">
              <div class="flex items-center gap-4">
                <h2 class="text-xl font-bold whitespace-nowrap truncate max-w-[300px]">{{ user.name }}</h2>
                <div class="flex items-start gap-1 text-sm text-gray-500 flex-1">
                  <SvgIcon name="verified" class="mt-0.5 flex-shrink-0" />
                  <span class="line-clamp-2">{{ user.bio || user.role }}</span>
                </div>
              </div>
              <div class="flex items-center" v-if="user?.login">
                <a 
                  :href="`https://github.com/${user.login}`" 
                  target="_blank"
                  class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm transition-colors"
                >
                  www.github.com/{{ user.login }}
                </a>
              </div>
            </div>
          </div>

          <!-- 主要内容区域 -->
          <div class="grid grid-cols-3 gap-6 mt-6">
            <!-- 左侧统计信息 -->
            <div class="space-y-4">
              <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-4">
                <h3 class="text-sm font-semibold text-gray-600 dark:text-gray-300 mb-3">GitHub Stats</h3>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-xs text-gray-500">Repositories</span>
                    <span class="text-sm font-medium">{{ stats?.repositories || 0 }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-xs text-gray-500">Stars</span>
                    <span class="text-sm font-medium">{{ stats?.stars || 0 }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-xs text-gray-500">Pull Requests</span>
                    <span class="text-sm font-medium">{{ stats?.pullRequests || 0 }}</span>
                  </div>
                </div>
              </div>

              <!-- 薪资信息 -->
              <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-4">
                <h3 class="text-sm font-semibold text-gray-600 dark:text-gray-300 mb-2">Estimated Income</h3>
                <div class="text-2xl font-bold text-green-600">${{ formatIncome(income) }}</div>
              </div>
            </div>

            <!-- 中间编程语言 -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-4">
              <h3 class="text-sm font-semibold text-gray-600 dark:text-gray-300 mb-3">Top Languages</h3>
              <div class="space-y-2">
                <div v-for="(percentage, language) in topLanguages" :key="language" class="flex justify-between">
                  <span class="text-xs text-gray-600 dark:text-gray-400">{{ language }}</span>
                  <span class="text-sm font-medium">{{ percentage }}%</span>
                </div>
              </div>
            </div>

            <!-- 右侧特色项目 -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-4">
              <h3 class="text-sm font-semibold text-gray-600 dark:text-gray-300 mb-3">Feature Project</h3>
              <div v-if="featureProject">
                <div class="text-sm font-medium mb-1">{{ featureProject.name }}</div>
                <div class="text-xs text-gray-500 mb-2">{{ featureProject.description }}</div>
                <div class="flex items-center gap-2 text-xs">
                  <span>⭐ {{ featureProject.stargazerCount }}</span>
                  <span>🍴 {{ featureProject.forkCount }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部版权信息（用于截图时显示） -->
          <div class="absolute bottom-4 right-4 flex items-center gap-2 text-xs text-gray-500">
            <span>Generated by DINQ</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import SvgIcon from '../SvgIcon/index.vue'

interface User {
  name: string;
  avatar: string;
  role: string;
  login?: string;
  bio?: string;
}

interface Stats {
  repositories: number;
  stars: number;
  pullRequests: number;
}

interface FeatureProject {
  name: string;
  description: string;
  stargazerCount: number;
  forkCount: number;
}

const props = defineProps<{
  user?: User;
  stats?: Stats;
  income?: number;
  languages?: Record<string, number>;
  languageTotal?: number;
  featureProject?: FeatureProject;
}>()

// 格式化收入显示
const formatIncome = (income?: number) => {
  if (!income) return '0'
  return income.toLocaleString()
}

// 计算顶级编程语言
const topLanguages = computed(() => {
  if (!props.languages || !props.languageTotal) return {}
  
  const sorted = Object.entries(props.languages)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .reduce((acc, [lang, count]) => {
      acc[lang] = Math.round((count / props.languageTotal!) * 100)
      return acc
    }, {} as Record<string, number>)
  
  return sorted
})
</script>

<style scoped>
.hidden-render-container {
  /* 确保元素完全隐藏但仍然存在于DOM中 */
  /* html2canvas可以捕获到这些元素 */
}
</style>
